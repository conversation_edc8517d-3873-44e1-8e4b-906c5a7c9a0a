import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, Database, Download, Trash2 } from "lucide-react";
import { Skeleton } from '@/components/ui/skeleton';
import api from "@/lib/api";

const DataManagement = () => {
  const [datasets, setDatasets] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDatasets();
  }, []);

  const loadDatasets = async () => {
    try {
      setLoading(true);
      const response = await api.get('/datasets');
      setDatasets(response.data);
    } catch (error) {
      console.error('Erro ao carregar datasets:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Gerenciamento de Dados</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="p-4">
              <Skeleton className="h-4 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Gerenciamento de Dados</h1>
        <Button onClick={loadDatasets}>Atualizar</Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {datasets.map((dataset) => (
          <Card key={dataset.id} className="p-4">
            <h3 className="font-semibold">{dataset.name}</h3>
            <p className="text-sm text-gray-600">{dataset.description}</p>
            <div className="mt-2">
              <Button variant="outline" size="sm" className="mr-2">
                Visualizar
              </Button>
              <Button variant="outline" size="sm">
                Editar
              </Button>
            </div>
          </Card>
        ))}

        {datasets.length === 0 && (
          <div className="col-span-full text-center py-8 text-gray-500">
            Nenhum dataset encontrado
          </div>
        )}
      </div>
    </div>
  );
};

export default DataManagement; 