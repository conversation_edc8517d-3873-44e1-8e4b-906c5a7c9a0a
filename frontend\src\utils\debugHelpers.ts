/**
 * Debug helpers for development
 */

export const clearAllData = () => {
  console.log('🧹 Limpando todos os dados...');

  // Limpar localStorage
  Object.keys(localStorage).forEach(key => {
    if (key.startsWith('vur_')) {
      localStorage.removeItem(key);
    }
  });

  // Limpar sessionStorage
  sessionStorage.clear();

  // Recarregar página
  window.location.reload();

  console.log('✅ Dados limpos!');
};

export const resetSystem = () => {
  console.log('🔄 Resetando sistema...');

  // Limpar dados
  clearAllData();

  console.log('✅ Sistema resetado!');
};

export const getSystemStatus = () => {
  console.log('📊 Status do Sistema:');

  // Listar todas as chaves no localStorage
  const storageKeys = Object.keys(localStorage).filter(key =>
    key.startsWith('vur_')
  );

  console.log('🗄️ Dados armazenados:', storageKeys.length);
  storageKeys.forEach(key => {
    console.log(`- ${key}`);
  });

  return {
    storageKeys,
  };
};

// Objeto global para debug
export const VUR_DEBUG = {
  clearData: clearAllData,
  reset: resetSystem,
  status: getSystemStatus,
};

// Expor objeto para console
if (process.env.NODE_ENV === 'development') {
  (window as any).VUR_DEBUG = VUR_DEBUG;

  console.log('🛠️ Helpers de Debug disponíveis no console:');
  console.log('VUR_DEBUG.clearData() - Limpar todos os dados');
  console.log('VUR_DEBUG.reset() - Resetar sistema');
  console.log('VUR_DEBUG.status() - Ver status do sistema');
}
